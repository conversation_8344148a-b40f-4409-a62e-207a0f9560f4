<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($pageTitle) ? $pageTitle . ' - ' . APP_NAME : APP_NAME . ' - Luxury Beauty Salon' ?></title>
    <meta name="description" content="<?= isset($pageDescription) ? $pageDescription : 'Experience advanced medical aesthetics at Redolence Medi Aesthetics. Professional treatments with cutting-edge technology and personalized care.' ?>">
    <meta name="keywords" content="medical aesthetics, beauty treatments, skincare, anti-aging, facial rejuvenation, cosmetic procedures, wellness, dermatology">
    <meta name="author" content="Redolence Medi Aesthetics">

    <!-- Open Graph -->
    <meta property="og:title" content="<?= isset($pageTitle) ? $pageTitle . ' - ' . APP_NAME : APP_NAME . ' - Luxury Beauty Salon' ?>">
    <meta property="og:description" content="<?= isset($pageDescription) ? $pageDescription : 'Experience advanced medical aesthetics at Redolence Medi Aesthetics. Professional treatments with cutting-edge technology and personalized care.' ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?= APP_URL ?>">
    <meta property="og:image" content="<?= APP_URL ?>/assets/images/og-image.jpg">

    <!-- Favicon -->
    <link rel="icon" type="image/jpeg" href="<?= getBasePath() ?>/assets/images/red.jpg">
    <link rel="apple-touch-icon" href="<?= getBasePath() ?>/assets/images/red.jpg">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9f4',
                            100: '#dcf4e6',
                            200: '#bce8d1',
                            300: '#8dd5b3',
                            400: '#5cb85c',
                            500: '#49a75c',
                            600: '#3d8b50',
                            700: '#336f42',
                            800: '#2d5a37',
                            900: '#264a2f',
                        },
                        secondary: {
                            50: '#f0f7ff',
                            100: '#e0efff',
                            200: '#b9dfff',
                            300: '#7cc8ff',
                            400: '#36b0ff',
                            500: '#5894d2',
                            600: '#4a7bc4',
                            700: '#3e65a3',
                            800: '#355485',
                            900: '#2d456d',
                        },
                        'redolence-green': '#49a75c',
                        'redolence-blue': '#5894d2',
                        'redolence-white': '#ffffff',
                        'green-light': '#5cb85c',
                        'green-dark': '#3d8b50',
                    },
                    fontFamily: {
                        sans: ['Inter', 'ui-sans-serif', 'system-ui'],
                        serif: ['Playfair Display', 'ui-serif', 'Georgia'],
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.5s ease-out',
                        'scale-in': 'scaleIn 0.3s ease-out',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        },
                        scaleIn: {
                            '0%': { transform: 'scale(0.95)', opacity: '0' },
                            '100%': { transform: 'scale(1)', opacity: '1' },
                        },
                    },
                }
            }
        }
    </script>

    <!-- Custom CSS -->
    <style>
        :root {
            --background: #ffffff;
            --foreground: #000000;
            --primary-green: #49a75c;
            --primary-blue: #5894d2;
            --green-light: #5cb85c;
            --green-dark: #3d8b50;
            --blue-light: #7cc8ff;
            --blue-dark: #3e65a3;
        }

        * {
            box-sizing: border-box;
            padding: 0;
            margin: 0;
        }

        html, body {
            max-width: 100vw;
            overflow-x: hidden;
            scroll-behavior: smooth;
        }

        body {
            background: var(--background);
            color: var(--foreground);
            font-family: 'Inter', ui-sans-serif, system-ui;
            line-height: 1.6;
            padding-top: 80px; /* Add padding for fixed header */
        }

        /* Enhanced Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f5f9;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #49a75c, #5894d2);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #3d8b50, #4a7bc4);
        }

        /* Custom Scrollbar for Service Dropdown */
        .custom-scrollbar::-webkit-scrollbar {
            width: 4px;
        }

        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 2px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #49a75c;
            border-radius: 2px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #2d6a3e;
        }

        /* Service Description Preview Styling */
        .service-description-preview {
            max-height: 60px;
            overflow: hidden;
            line-height: 1.4;
        }

        .service-description-preview h3 {
            font-size: 0.75rem;
            font-weight: 600;
            color: #374151;
            margin: 0 0 0.25rem 0;
        }

        .service-description-preview h4 {
            font-size: 0.7rem;
            font-weight: 600;
            color: #49a75c;
            margin: 0.25rem 0 0.125rem 0;
        }

        .service-description-preview p {
            font-size: 0.7rem;
            color: #6b7280;
            margin: 0.125rem 0;
            line-height: 1.3;
        }

        .service-description-preview ul {
            margin: 0.125rem 0;
            padding-left: 0.75rem;
        }

        .service-description-preview li {
            font-size: 0.65rem;
            color: #6b7280;
            margin: 0;
            line-height: 1.2;
        }

        .service-description-preview strong {
            font-weight: 600;
            color: #374151;
        }

        /* Enhanced Selection */
        ::selection {
            background: linear-gradient(135deg, #49a75c, #5894d2);
            color: white;
        }

        /* Modern Typography */
        .text-luxury {
            font-family: 'Montserrat', sans-serif;
            font-weight: 600;
            letter-spacing: -0.025em;
        }

        .text-medical {
            font-family: 'Inter', sans-serif;
            font-weight: 500;
        }

        .text-display {
            font-family: 'Poppins', sans-serif;
            font-weight: 700;
        }

        /* Enhanced Gradient Effects */
        .gradient-text {
            background: linear-gradient(135deg, #49a75c 0%, #5894d2 50%, #d4af37 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            background-size: 200% 200%;
            animation: gradientShift 4s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .gradient-bg {
            background: linear-gradient(135deg, #49a75c 0%, #5894d2 50%, #d4af37 100%);
            background-size: 200% 200%;
            animation: gradientShift 6s ease-in-out infinite;
        }

        /* Advanced Glass Effect */
        .glass-effect {
            backdrop-filter: blur(20px) saturate(180%);
            background: rgba(255, 255, 255, 0.85);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .glass-dark {
            backdrop-filter: blur(20px) saturate(180%);
            background: rgba(26, 26, 26, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Enhanced Navigation */
        .nav-link {
            position: relative;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            font-family: 'Inter', sans-serif;
            font-weight: 500;
            letter-spacing: -0.01em;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(73, 167, 92, 0.1), rgba(88, 148, 210, 0.1));
            border-radius: 12px;
            opacity: 0;
            transform: scale(0.8);
            transition: all 0.3s ease;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 3px;
            bottom: -6px;
            left: 50%;
            background: linear-gradient(135deg, #49a75c, #5894d2);
            border-radius: 2px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            transform: translateX(-50%);
        }

        .nav-link:hover::before {
            opacity: 1;
            transform: scale(1);
        }

        .nav-link:hover::after,
        .nav-link.active::after {
            width: 100%;
        }

        .nav-link:hover {
            transform: translateY(-2px);
            color: #49a75c;
        }

        /* Modern Header Styles */
        .header-scrolled {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .logo-container {
            transition: all 0.3s ease;
        }

        .logo-container:hover {
            transform: scale(1.05);
        }

        .logo-icon {
            background: linear-gradient(135deg, #49a75c, #5894d2);
            transition: all 0.3s ease;
        }

        .logo-container:hover .logo-icon {
            background: linear-gradient(135deg, #5894d2, #d4af37);
            transform: rotate(5deg);
        }

        /* Enhanced CTA Button */
        .cta-button {
            background: linear-gradient(135deg, #49a75c 0%, #5894d2 100%);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .cta-button:hover::before {
            left: 100%;
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(73, 167, 92, 0.3);
        }

        /* Hero carousel transitions */
        .hero-slide {
            transition: opacity 1s ease-in-out;
        }

        .hero-dot {
            transition: all 0.3s ease;
        }

        .hero-dot.active {
            background-color: var(--gold) !important;
        }

        /* Enhanced Hamburger menu animation */
        .hamburger {
            transition: all 0.3s ease;
        }

        .hamburger.active {
            transform: rotate(45deg);
        }

        .hamburger-line {
            transition: all 0.3s ease;
            transform-origin: center;
        }

        .hamburger.active .hamburger-line:nth-child(1) {
            transform: rotate(90deg) translateX(0px);
        }

        .hamburger.active .hamburger-line:nth-child(2) {
            opacity: 0;
        }

        .hamburger.active .hamburger-line:nth-child(3) {
            transform: rotate(-90deg) translateX(0px);
        }

        /* Dropdown menu animations */
        .group:hover .group-hover\\:opacity-100 {
            opacity: 1;
        }

        .group:hover .group-hover\\:visible {
            visibility: visible;
        }

        /* Line clamp utilities */
        .line-clamp-1 {
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* Refresh button animation */
        .refresh-spin {
            animation: spin 0.5s linear;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Auto-hide header */
        header {
            padding: 0px;
            transition: transform 0.3s ease;
        }
        header.hidden {
            transform: translateY(-100%);
        }
    </style>

    <!-- AJAX Reminder Processor -->
    <script src="<?= getBasePath() ?>/assets/js/reminder-processor.js"></script>
</head>
<body class="antialiased min-h-screen flex flex-col public-page">
    <!-- Header -->
    <header class="bg-white/95 backdrop-blur-md border-b border-gray-200 fixed top-0 left-0 right-0 z-50 shadow-lg">
        <nav class="mx-auto flex max-w-7xl items-center justify-between p-2 lg:px-8" aria-label="Global">
            <!-- Enhanced Modern Logo -->
            <div class="flex lg:flex-1">
                <a href="<?= getBasePath() ?>/" class="logo-container flex items-center space-x-4 group -m-1.5 p-1.5">
                    <div class="logo-icon w-12 h-12 rounded-2xl overflow-hidden shadow-xl group-hover:shadow-2xl transition-all duration-300 border-2 border-white">
                        <img src="<?= getBasePath() ?>/assets/images/red.jpg"
                             alt="Redolence Medi Aesthetics Logo"
                             class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300">
                    </div>
                    <div class="hidden sm:block">
                        <h1 class="text-xl font-heading font-bold gradient-text">Redolence</h1>
                        <p class="text-xs text-gray-600 font-medical -mt-1 tracking-wide">Medi Aesthetics</p>
                    </div>
                </a>
            </div>

            <!-- Enhanced Mobile Menu Button -->
            <div class="flex lg:hidden">
                <button type="button"
                        class="hamburger lg:hidden p-3 rounded-xl hover:bg-gray-100 transition-all duration-300"
                        id="mobile-menu-button">
                    <span class="sr-only">Open main menu</span>
                    <div class="w-6 h-6 flex flex-col justify-center items-center space-y-1">
                        <span class="hamburger-line w-6 h-0.5 bg-gray-700 block"></span>
                        <span class="hamburger-line w-6 h-0.5 bg-gray-700 block"></span>
                        <span class="hamburger-line w-6 h-0.5 bg-gray-700 block"></span>
                    </div>
                </button>
            </div>

            <!-- Desktop navigation -->
            <div class="hidden lg:flex lg:gap-x-12">
                <?php
                $basePath = getBasePath();
                $currentPath = $_SERVER['REQUEST_URI'];

                // Get featured services for dropdown (popular and featured services)
                try {
                    $featuredServices = $database->fetchAll(
                        "SELECT id, name, description, price, duration, category, featured, popular, new_treatment, image
                         FROM services
                         WHERE is_active = 1 AND (featured = 1 OR popular = 1 OR new_treatment = 1)
                         ORDER BY
                            CASE WHEN featured = 1 THEN 1 ELSE 2 END,
                            CASE WHEN popular = 1 THEN 1 ELSE 2 END,
                            CASE WHEN new_treatment = 1 THEN 1 ELSE 2 END,
                            name ASC
                         LIMIT 8"
                    );

                    // Get service categories for organization
                    $serviceCategories = $database->fetchAll(
                        "SELECT DISTINCT category FROM services WHERE is_active = 1 ORDER BY category"
                    );
                } catch (Exception $e) {
                    $featuredServices = [];
                    $serviceCategories = [];
                }
                ?>

                <!-- Home -->
                <?php $isActive = $currentPath === $basePath . '/'; ?>
                <a href="<?= $basePath ?>/"
                   class="nav-link px-4 py-2 text-gray-700 hover:text-redolence-green <?= $isActive ? 'active text-redolence-green' : '' ?>">
                    Home
                </a>

                <!-- Services with Enhanced Dropdown -->
                <?php $isServicesActive = $currentPath === $basePath . '/services' || strpos($currentPath, $basePath . '/services') === 0; ?>
                <div class="relative group">
                    <a href="<?= $basePath ?>/services"
                       class="nav-link flex items-center px-4 py-2 text-gray-700 hover:text-redolence-green <?= $isServicesActive ? 'active text-redolence-green' : '' ?>">
                        Services
                        <svg class="ml-2 h-4 w-4 transition-transform group-hover:rotate-180" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                        </svg>
                    </a>

                    <!-- Modern Services Dropdown -->
                    <div class="absolute top-full left-0 mt-3 w-96 bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-gray-200/50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform scale-95 group-hover:scale-100 z-50">

                        <!-- Header -->
                        <div class="px-6 py-4 border-b border-gray-100">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="font-bold text-gray-900">Our Services</h3>
                                    <p class="text-sm text-gray-500">Professional medical aesthetics treatments</p>
                                </div>
                                <div class="w-10 h-10 bg-gradient-to-br from-redolence-green to-redolence-blue rounded-xl flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <!-- Featured Services -->
                        <?php if (!empty($featuredServices)): ?>
                            <div class="px-6 py-4">
                                <div class="flex items-center mb-4">
                                    <span class="text-sm font-semibold text-redolence-green">✨ Featured Treatments</span>
                                </div>

                                <div class="grid grid-cols-1 gap-3 max-h-80 overflow-y-auto custom-scrollbar">
                                    <?php foreach ($featuredServices as $service): ?>
                                        <a href="service-detail.php?id=<?= $service['id'] ?>"
                                           class="group flex items-center p-3 rounded-xl hover:bg-gradient-to-r hover:from-redolence-green/10 hover:to-redolence-blue/10 transition-all duration-200 border border-transparent hover:border-redolence-green/20">

                                            <!-- Service Icon/Image -->
                                            <div class="w-12 h-12 bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl flex items-center justify-center mr-4 group-hover:from-redolence-green/20 group-hover:to-redolence-blue/20 transition-all duration-200">
                                                <?php if ($service['image']): ?>
                                                    <?php
                                                    $imageSrc = $service['image'];
                                                    if (!filter_var($imageSrc, FILTER_VALIDATE_URL)) {
                                                        $imageSrc = getBasePath() . '/uploads/' . ltrim($imageSrc, '/');
                                                    }
                                                    ?>
                                                    <img src="<?= htmlspecialchars($imageSrc) ?>"
                                                         alt="<?= htmlspecialchars(html_entity_decode($service['name'], ENT_QUOTES, 'UTF-8')) ?>"
                                                         class="w-full h-full object-cover rounded-xl">
                                                <?php else: ?>
                                                    <svg class="w-6 h-6 text-gray-500 group-hover:text-redolence-green transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                                                    </svg>
                                                <?php endif; ?>
                                            </div>

                                            <!-- Service Info -->
                                            <div class="flex-1 min-w-0">
                                                <div class="flex items-center mb-1">
                                                    <h4 class="font-semibold text-gray-900 group-hover:text-redolence-green transition-colors duration-200 truncate">
                                                        <?= htmlspecialchars(html_entity_decode($service['name'], ENT_QUOTES, 'UTF-8')) ?>
                                                    </h4>

                                                    <!-- Badges -->
                                                    <div class="flex items-center ml-2 space-x-1">
                                                        <?php if ($service['featured']): ?>
                                                            <span class="w-2 h-2 bg-yellow-400 rounded-full" title="Featured"></span>
                                                        <?php endif; ?>
                                                        <?php if ($service['popular']): ?>
                                                            <span class="w-2 h-2 bg-red-400 rounded-full" title="Popular"></span>
                                                        <?php endif; ?>
                                                        <?php if ($service['new_treatment']): ?>
                                                            <span class="w-2 h-2 bg-green-400 rounded-full" title="New"></span>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>

                                                <div class="text-xs text-gray-500 mb-2 service-description-preview">
                                                    <?php
                                                    $description = $service['description'] ?? '';

                                                    if (!empty($description)) {
                                                        // Decode HTML entities to fix &amp; issue
                                                        $description = html_entity_decode($description, ENT_QUOTES, 'UTF-8');

                                                        // Get plain text for length calculation
                                                        $plainText = strip_tags($description);
                                                        $plainText = trim(preg_replace('/\s+/', ' ', $plainText));

                                                        if (strlen($plainText) > 120) {
                                                            // For long content, show just a clean summary
                                                            $truncated = substr($plainText, 0, 120) . '...';
                                                            echo '<p>' . htmlspecialchars($truncated) . '</p>';
                                                        } else {
                                                            // For shorter content, render the HTML with allowed tags
                                                            $allowedTags = '<p><strong><em><ul><li><h3><h4><br>';
                                                            $cleanHtml = strip_tags($description, $allowedTags);

                                                            // Limit the number of list items to prevent overflow
                                                            $cleanHtml = preg_replace('/(<li>.*?<\/li>){4,}/s', '$1$2$3<li>...</li>', $cleanHtml);

                                                            echo $cleanHtml;
                                                        }
                                                    } else {
                                                        echo '<p>Professional medical aesthetic treatment</p>';
                                                    }
                                                    ?>
                                                </div>

                                                <div class="flex items-center justify-between">
                                                    <span class="text-xs text-gray-400 capitalize"><?= htmlspecialchars(html_entity_decode($service['category'], ENT_QUOTES, 'UTF-8')) ?></span>
                                                    <div class="flex items-center space-x-2 text-xs">
                                                        <?php if ($service['price']): ?>
                                                            <span class="text-redolence-green font-semibold">TSH <?= number_format($service['price']) ?></span>
                                                        <?php endif; ?>
                                                        <?php if ($service['duration']): ?>
                                                            <span class="text-gray-500"><?= $service['duration'] ?>min</span>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Arrow -->
                                            <svg class="w-4 h-4 text-gray-400 group-hover:text-redolence-green group-hover:translate-x-1 transition-all duration-200 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                            </svg>
                                        </a>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Footer -->
                        <div class="px-6 py-4 border-t border-gray-100 bg-gray-50/50 rounded-b-2xl">
                            <div class="flex items-center justify-between">
                                <a href="<?= $basePath ?>/services"
                                   class="flex items-center text-sm font-semibold text-redolence-green hover:text-redolence-green/80 transition-colors duration-200">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                    </svg>
                                    View All Services
                                </a>

                                <a href="customer/book/"
                                   class="inline-flex items-center bg-redolence-green text-white px-4 py-2 rounded-xl text-sm font-semibold hover:bg-redolence-green/90 transition-all duration-200 hover:scale-105">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    Book Now
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Other Navigation Items -->
                <?php
                $otherNavigation = [
                    ['name' => 'Offers', 'href' => $basePath . '/offers'],
                    ['name' => 'Gallery', 'href' => $basePath . '/gallery'],
                    ['name' => 'About', 'href' => $basePath . '/about'],
                    ['name' => 'Contact', 'href' => $basePath . '/contact'],
                ];

                foreach ($otherNavigation as $item):
                    $isActive = $currentPath === $item['href'] || ($item['href'] !== $basePath . '/' && strpos($currentPath, $item['href']) === 0);
                ?>
                    <a href="<?= $item['href'] ?>"
                       class="nav-link px-4 py-2 text-gray-700 hover:text-redolence-green <?= $isActive ? 'active text-redolence-green' : '' ?>">
                        <?= $item['name'] ?>
                    </a>
                <?php endforeach; ?>
            </div>

            <!-- Desktop CTA buttons -->
            <div class="hidden lg:flex lg:flex-1 lg:justify-end">
                <div class="flex items-center gap-x-3">
                    <!-- Wishlist Button -->
                    <?php
                    // Include wishlist functions for count
                    require_once __DIR__ . '/wishlist_functions.php';
                    $wishlistCount = isset($_SESSION['user_id'])
                        ? getWishlistCount($_SESSION['user_id'])
                        : getSessionWishlistCount();
                    ?>
                    <a href="<?= isLoggedIn() ? $basePath . '/customer/wishlist' : $basePath . '/auth/login.php?redirect=' . urlencode('/customer/wishlist') ?>"
                       class="relative inline-flex items-center rounded-lg px-3 py-2 text-sm font-semibold text-gray-700 hover:text-redolence-green transition-colors">
                        <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                        <?php if ($wishlistCount > 0): ?>
                            <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold px-1.5 py-0.5 rounded-full min-w-[18px] text-center" id="header-wishlist-badge">
                                <?= $wishlistCount ?>
                            </span>
                        <?php endif; ?>
                    </a>

                    <!-- Enhanced Book Now Button -->
                    <?php if (isLoggedIn()): ?>
                        <a href="<?= $basePath ?>/customer/book" class="cta-button inline-flex items-center text-white px-6 py-3 rounded-2xl font-semibold transition-all duration-300 shadow-lg relative overflow-hidden">
                            <svg class="mr-2 h-4 w-4 relative z-10" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 9l6-6m0 0l6 6m-6-6v12"></path>
                            </svg>
                            <span class="relative z-10">Book Consultation</span>
                        </a>
                    <?php else: ?>
                        <a href="<?= $basePath ?>/services" class="inline-flex items-center rounded-2xl border-2 border-redolence-green px-6 py-3 font-semibold text-redolence-green hover:bg-redolence-green hover:text-white transition-all duration-300 hover:scale-105">
                            <svg class="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 9l6-6m0 0l6 6m-6-6v12"></path>
                            </svg>
                            Explore Services
                        </a>
                    <?php endif; ?>

                    <!-- Enhanced User Menu -->
                    <?php if (isLoggedIn()): ?>
                        <?php $user = getCurrentUser(); ?>
                        <div class="relative group">
                            <!-- Modern Profile Button -->
                            <button class="flex items-center space-x-3 bg-white/90 backdrop-blur-sm border border-gray-200 rounded-2xl px-4 py-2.5 text-sm font-semibold text-gray-700 hover:text-redolence-green hover:border-redolence-green/30 hover:bg-white transition-all duration-300 shadow-sm hover:shadow-md group-hover:scale-105">
                                <!-- Profile Avatar -->
                                <div class="relative">
                                    <?php if ($user['role'] === 'ADMIN'): ?>
                                        <div class="w-8 h-8 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center shadow-lg">
                                            <svg class="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076-.124a6.57 6.57 0 01-.22-.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z" />
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                            </svg>
                                        </div>
                                        <div class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full border-2 border-white animate-pulse"></div>
                                    <?php elseif ($user['role'] === 'STAFF'): ?>
                                        <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center shadow-lg">
                                            <svg class="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 717.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
                                            </svg>
                                        </div>
                                        <div class="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full border-2 border-white animate-pulse"></div>
                                    <?php else: ?>
                                        <div class="w-8 h-8 bg-gradient-to-br from-redolence-green to-green-600 rounded-full flex items-center justify-center shadow-lg">
                                            <svg class="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M17.982 18.725A7.488 7.488 0 0012 15.75a7.488 7.488 0 00-5.982 2.975m11.963 0a9 9 0 10-11.963 0m11.963 0A8.966 8.966 0 0112 21a8.966 8.966 0 01-5.982-2.275M15 9.75a3 3 0 11-6 0 3 3 0 016 0z" />
                                            </svg>
                                        </div>
                                        <div class="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white animate-pulse"></div>
                                    <?php endif; ?>
                                </div>

                                <!-- User Info -->
                                <div class="flex flex-col items-start">
                                    <span class="text-sm font-bold text-gray-900">
                                        <?php if ($user['role'] === 'ADMIN'): ?>
                                            Admin Panel
                                        <?php elseif ($user['role'] === 'STAFF'): ?>
                                            Staff Portal
                                        <?php else: ?>
                                            <?= htmlspecialchars(explode(' ', $user['name'])[0]) ?>
                                        <?php endif; ?>
                                    </span>
                                    <span class="text-xs text-gray-500 capitalize"><?= strtolower($user['role']) ?></span>
                                </div>

                                <!-- Dropdown Arrow -->
                                <svg class="w-4 h-4 text-gray-400 group-hover:text-redolence-green transition-all duration-300 group-hover:rotate-180" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
                                </svg>
                            </button>

                            <!-- Enhanced Modern Dropdown Menu -->
                            <div class="absolute right-0 mt-3 w-72 bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-gray-200/50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform scale-95 group-hover:scale-100 z-50">
                                <!-- User Info Header -->
                                <div class="px-6 py-4 border-b border-gray-100">
                                    <div class="flex items-center space-x-3">
                                        <?php if ($user['role'] === 'ADMIN'): ?>
                                            <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center shadow-lg">
                                                <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076-.124a6.57 6.57 0 01-.22-.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z" />
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                </svg>
                                            </div>
                                        <?php elseif ($user['role'] === 'STAFF'): ?>
                                            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center shadow-lg">
                                                <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 717.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
                                                </svg>
                                            </div>
                                        <?php else: ?>
                                            <div class="w-12 h-12 bg-gradient-to-br from-redolence-green to-green-600 rounded-full flex items-center justify-center shadow-lg">
                                                <svg class="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M17.982 18.725A7.488 7.488 0 0012 15.75a7.488 7.488 0 00-5.982 2.975m11.963 0a9 9 0 10-11.963 0m11.963 0A8.966 8.966 0 0112 21a8.966 8.966 0 01-5.982-2.275M15 9.75a3 3 0 11-6 0 3 3 0 016 0z" />
                                                </svg>
                                            </div>
                                        <?php endif; ?>
                                        <div>
                                            <h3 class="font-bold text-gray-900">
                                                <?php if ($user['role'] === 'ADMIN'): ?>
                                                    Administrator
                                                <?php elseif ($user['role'] === 'STAFF'): ?>
                                                    Staff Member
                                                <?php else: ?>
                                                    <?= htmlspecialchars($user['name']) ?>
                                                <?php endif; ?>
                                            </h3>
                                            <p class="text-sm text-gray-500 capitalize"><?= strtolower($user['role']) ?> Account</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Menu Items -->
                                <div class="py-2">
                                    <!-- Dashboard -->
                                    <a href="<?= $basePath . ($user['role'] === 'ADMIN' ? '/admin' : ($user['role'] === 'STAFF' ? '/staff' : '/customer')) ?>"
                                       class="flex items-center px-6 py-3 text-sm font-medium text-gray-700 hover:bg-gradient-to-r hover:from-redolence-green/10 hover:to-redolence-blue/10 hover:text-redolence-green transition-all duration-200 group">
                                        <div class="w-10 h-10 bg-gray-100 rounded-xl flex items-center justify-center mr-3 group-hover:bg-redolence-green/10 group-hover:scale-110 transition-all duration-200">
                                            <svg class="w-5 h-5 text-gray-600 group-hover:text-redolence-green" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-semibold">Dashboard</div>
                                            <div class="text-xs text-gray-500">Manage your account</div>
                                        </div>
                                    </a>

                                    <?php if ($user['role'] === 'CUSTOMER'): ?>
                                        <!-- My Bookings -->
                                        <a href="<?= $basePath ?>/customer/bookings"
                                           class="flex items-center px-6 py-3 text-sm font-medium text-gray-700 hover:bg-gradient-to-r hover:from-redolence-green/10 hover:to-redolence-blue/10 hover:text-redolence-green transition-all duration-200 group">
                                            <div class="w-10 h-10 bg-gray-100 rounded-xl flex items-center justify-center mr-3 group-hover:bg-redolence-green/10 group-hover:scale-110 transition-all duration-200">
                                                <svg class="w-5 h-5 text-gray-600 group-hover:text-redolence-green" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 012.25-2.25h13.5A2.25 2.25 0 0121 7.5v11.25m-18 0A2.25 2.25 0 005.25 21h13.5a2.25 2.25 0 002.25-2.25m-18 0v-7.5A2.25 2.25 0 015.25 9h13.5a2.25 2.25 0 012.25 2.25v7.5" />
                                                </svg>
                                            </div>
                                            <div>
                                                <div class="font-semibold">My Bookings</div>
                                                <div class="text-xs text-gray-500">View appointments</div>
                                            </div>
                                        </a>

                                        <!-- Profile -->
                                        <a href="<?= $basePath ?>/customer/profile"
                                           class="flex items-center px-6 py-3 text-sm font-medium text-gray-700 hover:bg-gradient-to-r hover:from-redolence-green/10 hover:to-redolence-blue/10 hover:text-redolence-green transition-all duration-200 group">
                                            <div class="w-10 h-10 bg-gray-100 rounded-xl flex items-center justify-center mr-3 group-hover:bg-redolence-green/10 group-hover:scale-110 transition-all duration-200">
                                                <svg class="w-5 h-5 text-gray-600 group-hover:text-redolence-green" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M17.982 18.725A7.488 7.488 0 0012 15.75a7.488 7.488 0 00-5.982 2.975m11.963 0a9 9 0 10-11.963 0m11.963 0A8.966 8.966 0 0112 21a8.966 8.966 0 01-5.982-2.275M15 9.75a3 3 0 11-6 0 3 3 0 016 0z" />
                                                </svg>
                                            </div>
                                            <div>
                                                <div class="font-semibold">Profile Settings</div>
                                                <div class="text-xs text-gray-500">Update your info</div>
                                            </div>
                                        </a>
                                    <?php endif; ?>

                                    <!-- Divider -->
                                    <div class="border-t border-gray-100 my-2 mx-6"></div>

                                    <!-- Logout -->
                                    <a href="<?= $basePath ?>/auth/logout.php"
                                       class="flex items-center px-6 py-3 text-sm font-medium text-red-600 hover:bg-red-50 hover:text-red-700 transition-all duration-200 group rounded-b-2xl">
                                        <div class="w-10 h-10 bg-red-50 rounded-xl flex items-center justify-center mr-3 group-hover:bg-red-100 group-hover:scale-110 transition-all duration-200">
                                            <svg class="w-5 h-5 text-red-500 group-hover:text-red-600" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 9V5.25A2.25 2.25 0 0013.5 3h-6a2.25 2.25 0 00-2.25 2.25v13.5A2.25 2.25 0 007.5 21h6a2.25 2.25 0 002.25-2.25V15M12 9l-3 3m0 0l3 3m-3-3h12.75" />
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-semibold">Sign Out</div>
                                            <div class="text-xs text-red-400">End your session</div>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <a href="<?= $basePath ?>/auth/login.php" class="inline-flex items-center rounded-lg px-4 py-2 text-sm font-semibold text-gray-700 hover:text-redolence-green transition-colors">
                            <svg class="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M17.982 18.725A7.488 7.488 0 0012 15.75a7.488 7.488 0 00-5.982 2.975m11.963 0a9 9 0 10-11.963 0m11.963 0A8.966 8.966 0 0112 21a8.966 8.966 0 01-5.982-2.275M15 9.75a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                            Login
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </nav>
    </header>

    <script>
    // Function to refresh dropdown categories
    function refreshDropdownCategories() {
        const button = event.target;
        const svg = button.querySelector('svg');

        // Add spin animation
        if (svg) {
            svg.classList.add('refresh-spin');
            setTimeout(() => {
                svg.classList.remove('refresh-spin');
            }, 500);
        }

        // Reload the page to get new random categories
        setTimeout(() => {
            window.location.reload();
        }, 300);
    }
    </script>

    <script>
        let lastScrollTop = 0;
        window.addEventListener('scroll', function() {
            let currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;
            if (currentScrollTop > lastScrollTop) {
                document.querySelector('header').classList.add('hidden');
            } else {
                document.querySelector('header').classList.remove('hidden');
            }
            lastScrollTop = currentScrollTop;
        });
    </script>

    <main class="flex-1">
        <!-- Page content will be inserted here -->